<?php
// Teste de envio de formulário
require_once 'database/config.php';

// Simula dados do formulário
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST = [
    'name' => '<PERSON>',
    'email' => '<EMAIL>',
    'phone' => '11999887766',
    'instagram' => '@joao_teste',
    'age' => '28',
    'experience' => 'TENHO EXPERIÊNCIA',
    'availability' => 'Sim, me comprometo com a comunidade afins de ter resultados expressivos',
    'investment' => 'Claro, com certeza',
    'reason' => 'Estou muito motivado a aprender e crescer no marketing digital. Tenho experiência prévia e acredito que posso contribuir muito com a comunidade.'
];

echo "<h2>Teste de Processamento do Formulário</h2>";

// Processa como no index.php
$errors = [];
$success = false;

// Validação básica
if (empty($_POST['name'])) {
    $errors[] = 'Nome é obrigatório';
}
if (empty($_POST['email'])) {
    $errors[] = 'Email é obrigatório';
}
// ... outras validações ...

if (empty($errors)) {
    echo "<p style='color: green;'>✓ Validação passou!</p>";
    
    // Conecta ao banco
    $conn = conectarBanco();
    
    if ($conn) {
        echo "<p style='color: green;'>✓ Conectado ao banco!</p>";
        
        // Prepara dados
        $ip_address = '127.0.0.1';
        
        $sql = "INSERT INTO inscritos (nome, email, telefone, instagram, idade, experiencia, disponibilidade, investimento, motivo, ip_address) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        
        if ($stmt) {
            echo "<p style='color: green;'>✓ Statement preparado!</p>";
            
            $stmt->bind_param("ssssssssss", 
                $_POST['name'],
                $_POST['email'],
                $_POST['phone'],
                $_POST['instagram'],
                $_POST['age'],
                $_POST['experience'],
                $_POST['availability'],
                $_POST['investment'],
                $_POST['reason'],
                $ip_address
            );
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ Dados inseridos com sucesso! ID: " . $stmt->insert_id . "</p>";
                $success = true;
                
                // Testa arquivo texto
                $data = "Nome: {$_POST['name']}\n";
                $data .= "Email: {$_POST['email']}\n";
                $data .= "Telefone: {$_POST['phone']}\n";
                $data .= "Instagram: {$_POST['instagram']}\n";
                $data .= "Idade: {$_POST['age']}\n";
                $data .= "Experiência: {$_POST['experience']}\n";
                $data .= "Disponibilidade: {$_POST['availability']}\n";
                $data .= "Investimento: {$_POST['investment']}\n";
                $data .= "Motivo: {$_POST['reason']}\n";
                $data .= "Data: " . date('Y-m-d H:i:s') . "\n\n";
                
                $file = fopen("inscritos.txt", "a");
                if ($file) {
                    fwrite($file, $data);
                    fclose($file);
                    echo "<p style='color: green;'>✓ Dados salvos no arquivo texto!</p>";
                } else {
                    echo "<p style='color: red;'>✗ Erro ao salvar no arquivo texto</p>";
                }
                
            } else {
                echo "<p style='color: red;'>✗ Erro ao executar: " . $stmt->error . "</p>";
            }
            
            $stmt->close();
        } else {
            echo "<p style='color: red;'>✗ Erro ao preparar: " . $conn->error . "</p>";
        }
        
        $conn->close();
    } else {
        echo "<p style='color: red;'>✗ Erro ao conectar ao banco!</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Erros de validação:</p>";
    foreach ($errors as $error) {
        echo "<p style='color: red;'>- $error</p>";
    }
}

echo "<hr>";
echo "<p><a href='index.php'>Voltar ao formulário</a> | <a href='admin.php'>Ver painel admin</a> | <a href='test_db.php'>Testar banco</a></p>";
?> 