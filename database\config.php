<?php
/**
 * Configuração do Banco de Dados
 * Altere estas configurações de acordo com seu servidor
 */

define('DB_HOST', 'localhost');        // Host do banco de dados
define('DB_USER', 'root');             // Usuário do banco de dados
define('DB_PASS', 'vertrigo');                 // Senha do banco de dados
define('DB_NAME', 'form'); // Nome do banco de dados
define('DB_CHARSET', 'utf8mb4');       // Charset do banco de dados

/**
 * Função para conectar ao banco de dados
 * @return mysqli|false Retorna a conexão ou false em caso de erro
 */
function conectarBanco() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($conn->connect_error) {
        error_log("Erro de conexão com banco de dados: " . $conn->connect_error);
        return false;
    }
    
    // Define o charset
    if (!$conn->set_charset(DB_CHARSET)) {
        error_log("Erro ao definir charset: " . $conn->error);
    }
    
    error_log("Conexão com banco de dados estabelecida com sucesso");
    return $conn;
}

/**
 * Função para preparar strings para o banco
 * @param mixed $str String a ser tratada
 * @param mysqli $conn Conexão com o banco
 * @return string String tratada
 */
function prepararString($str, $conn) {
    return $conn->real_escape_string(trim($str));
} 