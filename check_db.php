<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Verificação do Banco de Dados</h1>";

// Primeiro tenta conectar sem selecionar o banco
$conn = new mysqli('localhost', 'root', '');

if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

// Verifica se o banco existe
$db_exists = $conn->query("SHOW DATABASES LIKE 'formulario_zucro'");
echo "<p>Banco de dados 'formulario_zucro' existe? " . ($db_exists->num_rows > 0 ? "SIM" : "NÃO") . "</p>";

if ($db_exists->num_rows == 0) {
    echo "<p style='color: red;'>O banco de dados não existe! Criando...</p>";
    
    // Cria o banco
    if ($conn->query("CREATE DATABASE IF NOT EXISTS formulario_zucro CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")) {
        echo "<p style='color: green;'>Banco de dados criado com sucesso!</p>";
    } else {
        echo "<p style='color: red;'>Erro ao criar banco de dados: " . $conn->error . "</p>";
    }
}

// Seleciona o banco
$conn->select_db('formulario_zucro');

// Verifica se a tabela existe
$table_exists = $conn->query("SHOW TABLES LIKE 'inscritos'");
echo "<p>Tabela 'inscritos' existe? " . ($table_exists->num_rows > 0 ? "SIM" : "NÃO") . "</p>";

if ($table_exists->num_rows == 0) {
    echo "<p style='color: red;'>A tabela não existe! Criando...</p>";
    
    // Cria a tabela
    $create_table = "CREATE TABLE IF NOT EXISTS inscritos (
        id INT(11) NOT NULL AUTO_INCREMENT,
        nome VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        telefone VARCHAR(20) NOT NULL,
        instagram VARCHAR(100) NOT NULL,
        idade INT(3) NOT NULL,
        experiencia VARCHAR(50) NOT NULL,
        disponibilidade TEXT NOT NULL,
        investimento TEXT NOT NULL,
        motivo TEXT NOT NULL,
        data_inscricao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        status ENUM('novo', 'analisado', 'aprovado', 'rejeitado') DEFAULT 'novo',
        observacoes TEXT,
        PRIMARY KEY (id),
        INDEX idx_email (email),
        INDEX idx_data (data_inscricao),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($create_table)) {
        echo "<p style='color: green;'>Tabela criada com sucesso!</p>";
    } else {
        echo "<p style='color: red;'>Erro ao criar tabela: " . $conn->error . "</p>";
    }
}

// Verifica os registros
$result = $conn->query("SELECT COUNT(*) as total FROM inscritos");
$count = $result->fetch_assoc()['total'];
echo "<p>Total de registros na tabela: " . $count . "</p>";

if ($count > 0) {
    echo "<h2>Últimos 5 registros:</h2>";
    $result = $conn->query("SELECT * FROM inscritos ORDER BY id DESC LIMIT 5");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Data</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 5px;'>" . $row['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($row['nome']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($row['email']) . "</td>";
        echo "<td style='padding: 5px;'>" . $row['data_inscricao'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>Não há registros na tabela.</p>";
    
    // Insere um registro de teste
    echo "<p>Inserindo registro de teste...</p>";
    $insert = $conn->prepare("INSERT INTO inscritos (nome, email, telefone, instagram, idade, experiencia, disponibilidade, investimento, motivo, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $nome = "Teste Automático";
    $email = "<EMAIL>";
    $telefone = "11999999999";
    $instagram = "@teste";
    $idade = "25";
    $experiencia = "TENHO EXPERIÊNCIA";
    $disponibilidade = "Sim, me comprometo";
    $investimento = "Claro, com certeza";
    $motivo = "Teste automático de inserção";
    $ip = "127.0.0.1";
    
    $insert->bind_param("ssssssssss", $nome, $email, $telefone, $instagram, $idade, $experiencia, $disponibilidade, $investimento, $motivo, $ip);
    
    if ($insert->execute()) {
        echo "<p style='color: green;'>Registro de teste inserido com sucesso!</p>";
    } else {
        echo "<p style='color: red;'>Erro ao inserir registro de teste: " . $insert->error . "</p>";
    }
}

$conn->close();

echo "<hr>";
echo "<p><a href='admin.php'>Ir para o painel admin</a> | <a href='test_db.php'>Ir para teste de banco</a></p>"; 