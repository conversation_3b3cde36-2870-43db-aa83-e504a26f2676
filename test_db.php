<?php
// Teste de conexão com banco de dados
require_once 'database/config.php';

echo "<h2>Teste de Banco de Dados</h2>";

// Testa a conexão
$conn = conectarBanco();

if ($conn) {
    echo "<p style='color: green;'>✓ Conexão com banco de dados estabelecida com sucesso!</p>";
    
    // Verifica se a tabela existe
    $result = $conn->query("SHOW TABLES LIKE 'inscritos'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Tabela 'inscritos' encontrada!</p>";
        
        // Mostra a estrutura da tabela
        echo "<h3>Estrutura da tabela 'inscritos':</h3>";
        $structure = $conn->query("DESCRIBE inscritos");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padr<PERSON></th><th>Extra</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td style='padding: 5px;'>" . $value . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        // Conta registros
        $count = $conn->query("SELECT COUNT(*) as total FROM inscritos");
        $total = $count->fetch_assoc()['total'];
        echo "<p><strong>Total de registros na tabela:</strong> $total</p>";
        
        // Testa inserção
        echo "<h3>Teste de inserção:</h3>";
        $test_sql = "INSERT INTO inscritos (nome, email, telefone, instagram, idade, experiencia, disponibilidade, investimento, motivo, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($test_sql);
        
        if ($stmt) {
            echo "<p style='color: green;'>✓ Statement preparado com sucesso!</p>";
            
            // Dados de teste
            $nome = "Teste " . date('Y-m-d H:i:s');
            $email = "<EMAIL>";
            $telefone = "11999999999";
            $instagram = "@teste";
            $idade = "25";
            $experiencia = "TENHO EXPERIÊNCIA";
            $disponibilidade = "Sim, me comprometo com a comunidade afins de ter resultados expressivos";
            $investimento = "Claro, com certeza";
            $motivo = "Este é um teste de inserção no banco de dados";
            $ip = "127.0.0.1";
            
            $stmt->bind_param("ssssssssss", $nome, $email, $telefone, $instagram, $idade, $experiencia, $disponibilidade, $investimento, $motivo, $ip);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ Registro de teste inserido com sucesso! ID: " . $stmt->insert_id . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Erro ao inserir: " . $stmt->error . "</p>";
            }
            
            $stmt->close();
        } else {
            echo "<p style='color: red;'>✗ Erro ao preparar statement: " . $conn->error . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Tabela 'inscritos' não encontrada!</p>";
        echo "<p>Execute o arquivo SQL em database/criar_banco.sql para criar a estrutura do banco.</p>";
    }
    
    $conn->close();
} else {
    echo "<p style='color: red;'>✗ Erro ao conectar com o banco de dados!</p>";
    echo "<p>Verifique as configurações em database/config.php</p>";
}

// Verifica se o arquivo de texto está sendo criado
echo "<h3>Teste de arquivo de texto:</h3>";
if (file_exists('inscritos.txt')) {
    echo "<p style='color: green;'>✓ Arquivo inscritos.txt existe!</p>";
    echo "<p>Tamanho: " . filesize('inscritos.txt') . " bytes</p>";
} else {
    echo "<p style='color: orange;'>⚠ Arquivo inscritos.txt não existe ainda (será criado no primeiro envio)</p>";
}

// Mostra informações do PHP
echo "<h3>Informações do ambiente:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQLi Extension: " . (extension_loaded('mysqli') ? 'Instalada' : 'Não instalada') . "</p>";

// Teste de carregamento do painel admin
echo "<h3>Teste de carregamento do painel admin:</h3>";
$conn = conectarBanco(); // Reconecta ao banco

if ($conn) {
    $sql = "SELECT * FROM inscritos ORDER BY data_inscricao DESC";
    $result = $conn->query($sql);

    if ($result) {
        echo "<p style='color: green;'>✓ Consulta SQL executada com sucesso!</p>";
        echo "<p>Número de registros encontrados: " . $result->num_rows . "</p>";
        
        if ($result->num_rows > 0) {
            echo "<h4>Últimos 5 registros:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Data</th></tr>";
            $count = 0;
            while ($row = $result->fetch_assoc()) {
                if ($count >= 5) break;
                echo "<tr>";
                echo "<td style='padding: 5px;'>" . $row['id'] . "</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($row['nome']) . "</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($row['email']) . "</td>";
                echo "<td style='padding: 5px;'>" . $row['data_inscricao'] . "</td>";
                echo "</tr>";
                $count++;
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠ Nenhum registro encontrado na tabela.</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Erro ao executar consulta: " . $conn->error . "</p>";
    }
    $conn->close();
} else {
    echo "<p style='color: red;'>✗ Erro ao conectar ao banco de dados</p>";
}
?> 