document.addEventListener('DOMContentLoaded', function() {
    // Elementos do formulário
    const form = document.querySelector('.zucro-form');
    const phoneInput = document.getElementById('phone');
    const instagramInput = document.getElementById('instagram');
    const submitBtn = document.querySelector('.submit-btn');
    const sections = document.querySelectorAll('.form-section');
    const nextButtons = document.querySelectorAll('.next-btn');
    const prevButtons = document.querySelectorAll('.prev-btn');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const reasonTextarea = document.getElementById('reason');
    const reasonCounter = document.getElementById('reasonCounter');
    
    // Elementos do popup
    const popup = document.getElementById('initialPopup');
    const closePopupBtn = document.getElementById('closePopup');
    
    console.log("Inicializando formulário...");
    
    // IMPORTANTE: Adiciona novalidate ao formulário para evitar erro de campos ocultos
    if (form) {
        form.setAttribute('novalidate', 'novalidate');
    }
    
    // Configuração dos radio buttons para selecionar corretamente
    const radioInputs = document.querySelectorAll('input[type="radio"]');
    radioInputs.forEach(radio => {
        // Verifica estado inicial
        if (radio.checked) {
            const card = radio.closest('.radio-card');
            if (card) {
                card.classList.add('selected');
            }
        }
        
        // Adiciona evento de click
        radio.addEventListener('change', function() {
            // Encontra o grupo de radio buttons (todos com o mesmo name)
            const name = this.name;
            const group = document.querySelectorAll(`input[name="${name}"]`);
            
            // Remove a classe 'selected' de todos os cards do grupo
            group.forEach(groupRadio => {
                const groupCard = groupRadio.closest('.radio-card');
                if (groupCard) {
                    groupCard.classList.remove('selected');
                }
            });
            
            // Adiciona classe 'selected' ao card selecionado
            const selectedCard = this.closest('.radio-card');
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }
        });
        
        // Adiciona evento de click no próprio card para melhorar UX
        const parentCard = radio.closest('.radio-card');
        if (parentCard) {
            parentCard.addEventListener('click', function() {
                const radioInput = this.querySelector('input[type="radio"]');
                if (radioInput) {
                    radioInput.checked = true;
                    // Dispara o evento change manualmente
                    const event = new Event('change');
                    radioInput.dispatchEvent(event);
                }
            });
        }
    });
    
    // Adiciona eventos para destacar inputs ao focar
    const allInputs = document.querySelectorAll('input, textarea');
    allInputs.forEach(input => {
        // Quando o input recebe foco
        input.addEventListener('focus', function() {
            // Encontrar o form-group pai
            const formGroup = this.closest('.form-group');
            if (formGroup) {
                formGroup.classList.add('focused');
                
                // Adiciona classe à div input-wrapper para styling adicional
                const inputWrapper = this.closest('.input-wrapper');
                if (inputWrapper) {
                    inputWrapper.classList.add('input-focused');
                }
            }
        });
        
        // Quando o input perde foco
        input.addEventListener('blur', function() {
            // Encontrar o form-group pai
            const formGroup = this.closest('.form-group');
            if (formGroup) {
                formGroup.classList.remove('focused');
                
                // Remove classe da div input-wrapper
                const inputWrapper = this.closest('.input-wrapper');
                if (inputWrapper) {
                    inputWrapper.classList.remove('input-focused');
                    
                    // Se o campo tem valor, marca como preenchido
                    if (this.value.trim() !== '') {
                        inputWrapper.classList.add('valid');
                    } else {
                        inputWrapper.classList.remove('valid');
                    }
                }
            }
        });
    });
    
    // Exibir o popup com um pequeno atraso para garantir uma experiência melhor
    setTimeout(function() {
        if (popup) {
            popup.classList.add('active');
        }
    }, 800);
    
    // Fechar o popup quando o botão de fechar for clicado
    if (closePopupBtn) {
        closePopupBtn.addEventListener('click', function() {
            popup.classList.remove('active');
            
            // Remover completamente após a animação terminar
            setTimeout(function() {
                popup.style.display = 'none';
            }, 300);
        });
    }
    
    // Inicializa o contador de caracteres
    if (reasonTextarea && reasonCounter) {
        reasonTextarea.addEventListener('input', function() {
            const currentLength = this.value.length;
            reasonCounter.textContent = `${currentLength}/500 caracteres`;
            
            // Atualiza cor com base no comprimento
            if (currentLength < 100) {
                reasonCounter.style.color = '#e53e3e';
            } else if (currentLength < 200) {
                reasonCounter.style.color = '#ed8936';
            } else {
                reasonCounter.style.color = '#48bb78';
            }
        });
        
        // Inicializa contador
        if (reasonTextarea.value) {
            const initialLength = reasonTextarea.value.length;
            reasonCounter.textContent = `${initialLength}/500 caracteres`;
        }
    }
    
    // Auto-foco no primeiro campo ao carregar
    const firstInput = document.querySelector('.form-section.active input');
    if (firstInput) {
        setTimeout(() => {
            firstInput.focus();
        }, 500);
    }

    // Função simplificada de navegação entre seções
    function showSection(sectionId) {
        console.log("Mostrando seção:", sectionId);
        
        // Esconde todas as seções
        sections.forEach(section => {
            section.style.display = 'none';
        });
        
        // Mostra a seção solicitada
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.style.display = 'block';
            
            // Atualiza o progresso
            let sectionIndex = 0;
            sections.forEach((section, index) => {
                if (section.id === sectionId) {
                    sectionIndex = index;
                }
            });
            
            const progress = (sectionIndex / (sections.length - 1)) * 100;
            if (progressFill) progressFill.style.width = progress + '%';
            if (progressText) progressText.textContent = `Progresso: ${Math.round(progress)}%`;
        }
    }
    
    // Manipuladores de eventos para botões de navegação
    if (nextButtons && nextButtons.length > 0) {
        nextButtons.forEach(button => {
            console.log("Configurando botão próximo");
            button.addEventListener('click', function() {
                const nextSection = this.getAttribute('data-next');
                console.log("Botão próximo clicado, indo para:", nextSection);
                showSection(nextSection);
            });
        });
    }
    
    if (prevButtons && prevButtons.length > 0) {
        prevButtons.forEach(button => {
            console.log("Configurando botão anterior");
            button.addEventListener('click', function() {
                const prevSection = this.getAttribute('data-prev');
                console.log("Botão anterior clicado, indo para:", prevSection);
                showSection(prevSection);
            });
        });
    }

    // Configuração do campo de telefone para formato DDNUMERO
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // Remove qualquer caractere que não seja número
            let value = this.value.replace(/\D/g, '');
            
            // Limita a 11 dígitos (DDD + Número)
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            
            // Atualiza o valor do campo
            this.value = value;
        });
    }

    // Formatação para o Instagram
    if (instagramInput) {
        instagramInput.addEventListener('input', function(e) {
            // Se o usuário não digitou @, adiciona automaticamente
            if (this.value && !this.value.startsWith('@')) {
                this.value = '@' + this.value;
            }
        });
    }
    
    // Validação de formulário com validação manual
    if (form) {
        form.addEventListener('submit', function(e) {
            // SOLUÇÃO: Remove temporariamente todos os atributos required para evitar erro de campos ocultos
            const allRequiredFields = form.querySelectorAll('[required]');
            allRequiredFields.forEach(field => {
                field.removeAttribute('required');
            });
            
            let isValid = true;
            let errorMessages = [];
            
            // Validação manual de todos os campos obrigatórios
            const name = document.getElementById('name');
            const email = document.getElementById('email');
            const phone = document.getElementById('phone');
            const instagram = document.getElementById('instagram');
            const age = document.getElementById('age');
            const experience = document.querySelector('input[name="experience"]:checked');
            const availability = document.querySelector('input[name="availability"]:checked');
            const investment = document.querySelector('input[name="investment"]:checked');
            const reason = document.getElementById('reason');
            
            // Validação do nome
            if (!name || !name.value.trim()) {
                isValid = false;
                errorMessages.push('Nome é obrigatório');
            }
            
            // Validação do email
            if (!email || !email.value.trim()) {
                isValid = false;
                errorMessages.push('Email é obrigatório');
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
                isValid = false;
                errorMessages.push('Email inválido');
            }
            
            // Validação do telefone
            if (!phone || !phone.value.trim()) {
                isValid = false;
                errorMessages.push('Telefone é obrigatório');
            } else {
                const phoneValue = phone.value.replace(/\D/g, '');
                if (phoneValue.length < 10 || phoneValue.length > 11) {
                    isValid = false;
                    errorMessages.push('Telefone deve ter entre 10 e 11 dígitos');
                }
            }
            
            // Validação do Instagram
            if (!instagram || !instagram.value.trim()) {
                isValid = false;
                errorMessages.push('Instagram é obrigatório');
            }
            
            // Validação da idade
            if (!age || !age.value.trim()) {
                isValid = false;
                errorMessages.push('Idade é obrigatória');
            }
            
            // Validação da experiência
            if (!experience) {
                isValid = false;
                errorMessages.push('Selecione sua experiência');
            }
            
            // Validação da disponibilidade
            if (!availability) {
                isValid = false;
                errorMessages.push('Selecione sua disponibilidade');
            }
            
            // Validação do investimento
            if (!investment) {
                isValid = false;
                errorMessages.push('Selecione sobre o investimento');
            }
            
            // Validação do motivo
            if (!reason || !reason.value.trim()) {
                isValid = false;
                errorMessages.push('Motivo é obrigatório');
            }
            
            if (!isValid) {
                e.preventDefault();
                
                // Restaura os atributos required se a validação falhar
                allRequiredFields.forEach(field => {
                    field.setAttribute('required', 'required');
                });
                
                if (errorMessages.length > 0) {
                    showToast(errorMessages[0], 'error');
                }
                return false;
            }
            
            // Se tudo estiver ok, continua com o envio
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            submitBtn.disabled = true;
        });
    }
    
    // Sistema de Toast para notificações
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        let icon = '';
        switch(type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-times-circle"></i>';
                break;
        }
        
        toast.innerHTML = `${icon} ${message}`;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
    
    // Inicializa a exibição da primeira seção
    console.log("Seções disponíveis:", sections.length);
    if (sections.length > 0) {
        // Define a primeira seção como visível
        sections.forEach((section, index) => {
            if (index === 0) {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        });
    }
}); 