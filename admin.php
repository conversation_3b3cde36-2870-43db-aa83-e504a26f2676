<?php
session_start();

// Debug da sessão
error_log("Debug - Status da sessão: " . 
          "session_id=" . session_id() . ", " .
          "admin_logged_in=" . (isset($_SESSION['admin_logged_in']) ? $_SESSION['admin_logged_in'] : 'não definido'));

// Credenciais do administrador (em produção, use hash de senha e banco de dados)
$admin_username = 'admin';
$admin_password = 'zucro2025'; // Mude isso em produção!

// Processar login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username === $admin_username && $password === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
        header('Location: admin.php');
        exit;
    } else {
        $error = 'Usuário ou senha incorretos!';
    }
}

// Processar logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// Se não estiver logado, mostrar formulário de login
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Área Administrativa</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
        }

        button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Área Administrativa</h1>
            <p>Faça login para continuar</p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Usuário</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Senha</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" name="login">Entrar</button>
        </form>
    </div>
</body>
</html>
<?php
    exit;
}// Se chegou aqui, está logado - mostrar painel administrativo?><!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel Administrativo - Formulários Recebidos</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f5f7fa;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .stat-info h3 {
            color: #666;
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 5px;
        }

        .stat-info p {
            color: #333;
            font-size: 28px;
            font-weight: 600;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .filter-btn {
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .filter-btn:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }

        .form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .form-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }

        .card-header h3 {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .card-header .date {
            font-size: 14px;
            opacity: 0.9;
        }

        .card-body {
            padding: 20px;
        }

        .info-row {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }

        .info-label {
            font-weight: 600;
            color: #666;
            min-width: 120px;
            font-size: 14px;
        }

        .info-value {
            color: #333;
            flex: 1;
            font-size: 14px;
            word-break: break-word;
        }

        .badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-experiencia {
            background: #e3f2fd;
            color: #1976d2;
        }

        .badge-disponivel {
            background: #e8f5e9;
            color: #388e3c;
        }

        .badge-investir {
            background: #fff3e0;
            color: #f57c00;
        }

        .badge-sem-investimento {
            background: #fce4ec;
            color: #d81b60;
        }

        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .no-results i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #ddd;
        }

        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: 1fr;
            }
            
            .filters {
                flex-direction: column;
            }
            
            .search-box {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <h1><i class="fas fa-user-shield"></i> Painel Administrativo</h1>
            <a href="?logout=1" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> Sair
            </a>
        </div>
    </header>
    <div class="container">
        <?php
        // Incluir configuração do banco de dados
        require_once 'database/config.php';
        
        // Ler formulários do banco de dados
        $formularios = [];
        $total_formularios = 0;
        $com_experiencia = 0;
        
        error_log("Iniciando carregamento dos formulários");
        
        // Forçar exibição de erros
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        
        $conn = conectarBanco();
        
        if ($conn) {
            error_log("Conexão estabelecida, executando consulta");
            
            // Debug da conexão
            echo "<!-- Debug: Conexão estabelecida -->\n";
            
            $sql = "SELECT * FROM inscritos ORDER BY data_inscricao DESC";
            $result = $conn->query($sql);
            
            if ($result) {
                error_log("Consulta executada com sucesso. Número de registros: " . $result->num_rows);
                echo "<!-- Debug: " . $result->num_rows . " registros encontrados -->\n";
                
                while ($row = $result->fetch_assoc()) {
                    $formularios[] = $row;
                    error_log("Registro carregado - ID: " . ($row['id'] ?? 'N/A'));
                }
            } else {
                error_log("Erro na consulta SQL: " . $conn->error);
                echo "<!-- Debug: Erro na consulta - " . $conn->error . " -->\n";
            }
            
            $conn->close();
        } else {
            error_log("Falha ao conectar ao banco de dados no painel admin");
            echo "<!-- Debug: Falha na conexão com o banco -->\n";
        }
        
        $total_formularios = count($formularios);
        error_log("Total de formulários carregados: " . $total_formularios);
        echo "<!-- Debug: Total de formulários: " . $total_formularios . " -->\n";
        
        // Debug dos dados
        echo "<!-- Debug: Conteúdo de \$formularios: " . print_r($formularios, true) . " -->\n";
        
        $com_experiencia = count(array_filter($formularios, function($f) {
            return isset($f['experiencia']) && $f['experiencia'] === 'TENHO EXPERIÊNCIA';
        }));
        ?>
        
        <!-- Estatísticas -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3>Total de Inscritos</h3>
                    <p><?php echo isset($total_formularios) ? $total_formularios : 0; ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-info">
                    <h3>Com Experiência</h3>
                    <p><?php echo isset($com_experiencia) ? $com_experiencia : 0; ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-info">
                    <h3>Taxa de Experiência</h3>
                    <p><?php echo (isset($total_formularios) && $total_formularios > 0 && isset($com_experiencia)) ? round(($com_experiencia / $total_formularios) * 100) : 0; ?>%</p>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="search" placeholder="Buscar por nome, email ou Instagram...">
            </div>
            <button class="filter-btn" onclick="filterByExperience()">
                <i class="fas fa-filter"></i> Com Experiência
            </button>
            <button class="filter-btn" onclick="filterByInvestment('com')">
                <i class="fas fa-check-circle"></i> Com Investimento
            </button>
            <button class="filter-btn" onclick="filterByInvestment('sem')">
                <i class="fas fa-times-circle"></i> Sem Investimento
            </button>
            <button class="filter-btn" onclick="resetFilters()">
                <i class="fas fa-redo"></i> Limpar Filtros
            </button>
        </div>

        <!-- Cards dos Formulários -->
        <div class="cards-grid" id="cardsContainer">
            <?php 
            // Debug antes da verificação
            echo "<!-- Debug: Antes do if - isset(formularios)=" . isset($formularios) . ", empty(formularios)=" . empty($formularios) . " -->\n";
            
            if (!isset($formularios) || empty($formularios)): 
                echo "<!-- Debug: Entrou no if de formulários vazios -->\n";
            ?>
                <div class="no-results">
                    <i class="fas fa-inbox"></i>
                    <h2>Nenhum formulário recebido ainda</h2>
                    <p>Os formulários enviados aparecerão aqui.</p>
                </div>
            <?php else: 
                echo "<!-- Debug: Entrou no else para exibir cards. Total: " . count($formularios) . " -->\n";
                
                foreach ($formularios as $form): 
                    error_log("Debug - Processando formulário ID: " . ($form['id'] ?? 'N/A'));
                    echo "<!-- Debug: Processando form ID=" . ($form['id'] ?? 'N/A') . " -->\n";
            ?>
                    <div class="form-card" data-nome="<?php echo strtolower($form['nome'] ?? ''); ?>"
                         data-email="<?php echo strtolower($form['email'] ?? ''); ?>"
                         data-instagram="<?php echo strtolower($form['instagram'] ?? ''); ?>"
                         data-experiencia="<?php echo $form['experiencia'] ?? ''; ?>"
                         data-investimento="<?php echo $form['investimento'] ?? ''; ?>">
                        <div class="card-header">
                            <h3><?php echo htmlspecialchars($form['nome'] ?? 'Sem nome'); ?></h3>
                            <div class="date">
                                <i class="far fa-calendar"></i>
                                <?php 
                                $data = $form['data_inscricao'] ?? '';
                                if ($data) {
                                    echo date('d/m/Y H:i', strtotime($data));
                                } else {
                                    echo 'Data não disponível';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="info-row">
                                <span class="info-label"><i class="fas fa-envelope"></i> Email:</span>
                                <span class="info-value"><?php echo htmlspecialchars($form['email'] ?? ''); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label"><i class="fas fa-phone"></i> Telefone:</span>
                                <span class="info-value"><?php echo htmlspecialchars($form['telefone'] ?? ''); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label"><i class="fab fa-instagram"></i> Instagram:</span>
                                <span class="info-value"><?php echo htmlspecialchars($form['instagram'] ?? ''); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label"><i class="fas fa-birthday-cake"></i> Idade:</span>
                                <span class="info-value"><?php echo htmlspecialchars($form['idade'] ?? ''); ?> anos</span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Experiência:</span>
                                <span class="info-value">
                                    <?php if (($form['experiencia'] ?? '') === 'TENHO EXPERIÊNCIA'): ?>
                                        <span class="badge badge-experiencia">Com Experiência</span>
                                    <?php else: ?>
                                        <span class="badge">Sem Experiência</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Disponibilidade:</span>
                                <span class="info-value">
                                    <?php if (strpos($form['disponibilidade'] ?? '', 'Sim') !== false): ?>
                                        <span class="badge badge-disponivel">Disponível</span>
                                    <?php else: ?>
                                        <span class="badge">Indisponível</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">Investimento:</span>
                                <span class="info-value">
                                    <?php if (strpos($form['investimento'] ?? '', 'Claro') !== false): ?>
                                        <span class="badge badge-investir">Pronto para Investir</span>
                                    <?php else: ?>
                                        <span class="badge badge-sem-investimento">Sem recursos no momento</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label"><i class="fas fa-comment"></i> Motivo:</span>
                                <span class="info-value"><?php echo htmlspecialchars($form['motivo'] ?? ''); ?></span>
                            </div>
                            
                            <?php if (!empty($form['status']) && $form['status'] !== 'novo'): ?>
                            <div class="info-row">
                                <span class="info-label">Status:</span>
                                <span class="info-value">
                                    <span class="badge badge-<?php echo $form['status']; ?>"><?php echo ucfirst($form['status']); ?></span>
                                </span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
            <?php 
                endforeach;
                echo "<!-- Debug: Fim do loop de cards -->\n";
            endif; 
            ?>
        </div>
    </div>

    <script>
        // Função de busca
        document.getElementById('search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const cards = document.querySelectorAll('.form-card');
            
            cards.forEach(card => {
                const nome = card.dataset.nome || '';
                const email = card.dataset.email || '';
                const instagram = card.dataset.instagram || '';
                
                if (nome.includes(searchTerm) || email.includes(searchTerm) || instagram.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // Filtrar por experiência
        function filterByExperience() {
            const cards = document.querySelectorAll('.form-card');
            cards.forEach(card => {
                if (card.dataset.experiencia === 'TENHO EXPERIÊNCIA') {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Filtrar por investimento
        function filterByInvestment(tipo) {
            const cards = document.querySelectorAll('.form-card');
            cards.forEach(card => {
                const investimento = card.dataset.investimento || '';
                if (tipo === 'com' && investimento.includes('Claro')) {
                    card.style.display = 'block';
                } else if (tipo === 'sem' && investimento.includes('Não')) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Limpar filtros
        function resetFilters() {
            document.getElementById('search').value = '';
            const cards = document.querySelectorAll('.form-card');
            cards.forEach(card => {
                card.style.display = 'block';
            });
        }
    </script>
</body>
</html> 