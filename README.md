# Formulário Zucro - Inscrição para Subsócios

Este é um formulário interativo e intuitivo em PHP para inscrição de subsócios da Zucro, baseado no formulário original do Google Forms.

## Requisitos

- Servidor web com suporte a PHP (como XAMPP, WAMP, LAMP)
- PHP 7.0 ou superior

## Características

- Design responsivo e moderno
- Validação de campos em tempo real
- Feedback visual para os usuários
- Salvamento dos dados em arquivo de texto
- Possibilidade de integração com banco de dados

## Arquivos do Projeto

- `index.php` - Página principal com o formulário
- `css/style.css` - Estilos do formulário
- `js/script.js` - Scripts para interatividade e validação
- `inscritos.txt` - Arquivo gerado automaticamente com os dados dos inscritos

## Como Usar

1. Clone ou baixe este repositório para o diretório web do seu servidor (ex: htdocs, www, public_html)
2. Certifique-se de que o servidor web e o PHP estão funcionando
3. Acesse o formulário pelo navegador (ex: http://localhost/formulariozucro/)
4. Os dados enviados serão salvos no arquivo `inscritos.txt`

## Personalização

Você pode personalizar facilmente este formulário:

- Edite o arquivo `css/style.css` para alterar cores, fontes e layout
- Modifique `index.php` para alterar os campos ou adicionar novos
- Adapte `js/script.js` para adicionar novas validações ou comportamentos

## Integração com Banco de Dados

Para integrar com um banco de dados, siga os passos:

1. Crie um banco de dados e uma tabela para armazenar as inscrições
2. Edite o arquivo `index.php` e modifique a seção de processamento do formulário
3. Substitua o código de gravação em arquivo por uma conexão com banco de dados

Exemplo de código para MySQL:

```php
// Configuração do banco de dados
$host = 'localhost';
$user = 'root';
$password = '';
$dbname = 'zucro_inscricoes';

// Conexão
$conn = new mysqli($host, $user, $password, $dbname);

// Verifica a conexão
if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

// Insere os dados
$sql = "INSERT INTO inscricoes (nome, email, telefone, instagram, idade, experiencia, disponibilidade, investimento, motivo, data_inscricao)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

$stmt = $conn->prepare($sql);
$stmt->bind_param("ssssissss", $name, $email, $phone, $instagram, $age, $experience, $availability, $investment, $reason);
$stmt->execute();

$stmt->close();
$conn->close();
```

## Segurança

Este formulário implementa validação básica, mas para uso em produção recomenda-se:

- Implementar proteção contra CSRF (Cross-Site Request Forgery)
- Adicionar validação mais rigorosa no lado do servidor
- Considerar o uso de HTTPS para proteger os dados transmitidos
- Implementar proteção contra ataques de injeção SQL se usar banco de dados

## Suporte

Para dúvidas ou suporte, entre em contato com o desenvolvedor.

## 🚀 Implantação

1. Faça o upload dos arquivos para seu servidor web
2. Certifique-se de que o servidor tenha suporte a PHP
3. Configure as permissões de escrita para o arquivo `inscritos.txt`
4. Acesse através do navegador

## 🔐 Área Administrativa

### Acesso ao Painel
- URL: `seu-dominio.com/admin.php`
- Usuário: `admin`
- Senha: `zucro2025`

### Funcionalidades do Painel Admin

1. **Login Seguro**: Sistema de autenticação com sessão PHP
2. **Dashboard com Estatísticas**:
   - Total de inscritos
   - Quantidade com experiência
   - Taxa percentual de experiência
3. **Visualização em Cards**: Cada formulário é exibido em um card moderno com todas as informações
4. **Sistema de Busca**: Pesquise por nome, email ou Instagram
5. **Filtros**: Filtre rapidamente candidatos com experiência
6. **Interface Responsiva**: Funciona perfeitamente em desktop e mobile

### Segurança
⚠️ **IMPORTANTE**: Para uso em produção, implemente:
- Hash de senha (use `password_hash()` do PHP)
- Banco de dados para credenciais
- HTTPS obrigatório
- Proteção contra força bruta
- Logs de acesso

## 📱 Responsividade

O sistema é totalmente responsivo e se adapta a diferentes tamanhos de tela:
- Desktop: Layout em grid com múltiplas colunas
- Tablet: Layout adaptativo
- Mobile: Layout em coluna única

## 🛡️ Segurança

// ... existing code ... 