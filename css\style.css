/* Reset e estilos básicos */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Se<PERSON>e <PERSON>', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(-45deg, #480EB7, #6C3CE6, #5930C0, #3F1A9A);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    min-height: 100vh;
    padding: 20px;
    position: relative;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    pointer-events: none;
    z-index: -1;
}

@keyframes gradientBG {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.container {
    max-width: 1000px;
    margin: 0 auto;
}

.form-container {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(50, 10, 100, 0.25), 0 10px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
    animation: float 6s ease-in-out infinite;
    position: relative;
}

@keyframes float {
    0% {
        transform: translateY(0px);
        box-shadow: 0 15px 35px rgba(50, 10, 100, 0.25), 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: translateY(-10px);
        box-shadow: 0 25px 45px rgba(50, 10, 100, 0.3), 0 15px 20px rgba(0, 0, 0, 0.15);
    }
    100% {
        transform: translateY(0px);
        box-shadow: 0 15px 35px rgba(50, 10, 100, 0.25), 0 10px 15px rgba(0, 0, 0, 0.1);
    }
}

/* Banner */
.banner-container {
    width: 100%;
    overflow: hidden;
    max-height: 250px; 
    position: relative;
}

.banner-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30px;
    background: linear-gradient(to top, rgba(255,255,255,1), rgba(255,255,255,0));
    z-index: 2;
}

.banner-image {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    object-position: center top;
    transition: transform 0.5s ease;
}

.banner-container:hover .banner-image {
    transform: scale(1.02);
}

/* Cabeçalho */
header {
    padding: 30px;
    background: linear-gradient(135deg, #480EB7 0%, #6C3CE6 100%);
    color: white;
    text-align: center;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-transform: uppercase;
    font-weight: 800;
}

header h2 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.alert {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    border-left: 5px solid #ff9800;
}

.alert p {
    margin-bottom: 10px;
}

.alert p:last-child {
    margin-bottom: 0;
}

/* Benefícios */
.benefits {
    padding: 30px;
    background-color: #f9f9f9;
}

.benefits h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #480EB7;
    font-weight: 600;
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

.benefits h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #480EB7, #6C3CE6);
    border-radius: 3px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
    margin-bottom: 30px;
}

.benefit-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    border-top: 4px solid #480EB7;
    display: flex;
    min-height: 140px;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(72, 14, 183, 0.15);
}

.benefit-icon {
    background: linear-gradient(135deg, #480EB7, #6C3CE6);
    color: white;
    width: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
}

.benefit-icon i {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.benefit-content {
    padding: 15px;
    flex: 1;
}

.benefit-content h4 {
    color: #333;
    font-size: 1rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.benefit-content p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.benefit-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #6C3CE6;
    color: white;
    padding: 3px 8px;
    font-size: 0.7rem;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: 700;
}

/* Highlight section styling */
.highlight {
    background-color: #4A0DB3;
    margin-top: 25px;
    padding: 20px;
    border-radius: 10px;
    color: white;
    box-shadow: 0 5px 15px rgba(72, 14, 183, 0.2);
}

.highlight p {
    margin-bottom: 10px;
    line-height: 1.5;
}

.highlight p:last-child {
    margin-bottom: 0;
}

.highlight strong {
    font-weight: 700;
}

.highlight em {
    opacity: 0.8;
    font-size: 0.9rem;
}

/* Animação para os cards de benefícios */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.benefit-card {
    animation: fadeInUp 0.5s ease backwards;
}

.benefit-card:nth-child(1) { animation-delay: 0.1s; }
.benefit-card:nth-child(2) { animation-delay: 0.2s; }
.benefit-card:nth-child(3) { animation-delay: 0.3s; }
.benefit-card:nth-child(4) { animation-delay: 0.4s; }
.benefit-card:nth-child(5) { animation-delay: 0.5s; }
.benefit-card:nth-child(6) { animation-delay: 0.6s; }
.benefit-card:nth-child(7) { animation-delay: 0.7s; }
.benefit-card:nth-child(8) { animation-delay: 0.8s; }

/* Responsividade para os cards de benefícios */
@media (max-width: 768px) {
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .benefit-card {
        min-height: auto;
    }
}

/* Barra de Progresso */
.progress-container {
    padding: 15px 30px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.progress-bar {
    height: 8px;
    background-color: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #480EB7 0%, #6C3CE6 100%);
    transition: width 0.5s ease;
}

.progress-text {
    text-align: right;
    font-size: 0.8rem;
    color: #718096;
}

/* Seções do Formulário */
.form-section {
    padding: 20px 0;
    transition: all 0.3s ease;
}

/* Corrigindo a exibição das seções */
.form-section#section1,
.form-section#section2,
.form-section#section3,
.form-section#section4 {
    display: none; /* Por padrão todas seções estão escondidas */
}

/* Mensagens de erro e sucesso */
.error-container {
    padding: 15px;
    background-color: #fff5f5;
    border-left: 5px solid #f56565;
    margin: 20px 30px 0;
}

.error-container h3 {
    color: #c53030;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.error-container h3 i {
    margin-right: 10px;
    color: #f56565;
}

.error-container ul {
    list-style-type: disc;
    margin-left: 20px;
}

.success-message {
    padding: 30px;
    background-color: #f0fff4;
    text-align: center;
    border-left: 5px solid #48bb78;
    margin: 30px;
}

.success-message h3 {
    color: #2f855a;
    font-size: 1.5rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.success-message h3 i {
    margin-right: 10px;
    color: #48bb78;
}

.success-animation {
    margin: 20px 0;
}

/* Formulário */
.zucro-form {
    padding: 30px;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
    transition: all 0.3s ease;
}

.form-group.required label:after {
    content: " *";
    color: #e53e3e;
}

.form-group.spotlight {
    background-color: #f0f9ff;
    padding: 25px;
    border-radius: 10px;
    border-left: 5px solid #4299e1;
    box-shadow: 0 5px 15px rgba(66, 153, 225, 0.1);
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
}

.field-icon {
    margin-right: 8px;
    color: #480EB7;
}

small {
    display: block;
    color: #718096;
    margin-top: 5px;
    font-style: italic;
}

.field-tip {
    color: #e53e3e;
    font-weight: 500;
    font-style: normal;
}

.field-note {
    display: block;
    margin-bottom: 10px;
}

.field-important {
    color: #2d3748;
    font-weight: 500;
    margin-bottom: 15px;
}

.question-note, .question-highlight {
    margin-bottom: 15px;
    font-size: 0.95rem;
    color: #4a5568;
}

.question-highlight {
    font-weight: 500;
    background-color: #ebf8ff;
    padding: 8px 12px;
    border-radius: 4px;
    color: #2c5282;
    margin-top: -5px;
}

.input-wrapper {
    position: relative;
}

.input-wrapper.input-focused {
    background-color: #f8f5ff;
    border-radius: 5px;
    box-shadow: 0 0 15px rgba(72, 14, 183, 0.08);
    transition: all 0.3s ease;
}

input[type="text"],
input[type="email"],
input[type="number"],
textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #cbd5e0;
    border-radius: 5px;
    font-size: 1rem;
    transition: all 0.3s;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
textarea:focus {
    border-color: #6C3CE6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(72, 14, 183, 0.2);
    background-color: #f8f5ff;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.valid-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #48bb78;
    opacity: 0;
    transition: opacity 0.3s;
}

.input-wrapper.valid .valid-indicator {
    opacity: 1;
}

.field-error {
    color: #e53e3e;
    font-size: 0.85rem;
    margin-top: 5px;
}

.field-tooltip {
    position: absolute;
    left: 0;
    top: calc(100% + 5px);
    background-color: #2d3748;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s, transform 0.3s;
    pointer-events: none;
    width: max-content;
    max-width: 300px;
}

.field-tooltip:after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 20px;
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent #2d3748 transparent;
}

.input-wrapper:hover .field-tooltip {
    opacity: 1;
    transform: translateY(0);
}

.textarea-wrapper {
    margin-bottom: 30px;
}

.char-counter {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #718096;
    font-size: 0.8rem;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 3px;
}

/* Radio estilizado */
.radio-group {
    margin-top: 10px;
}

.radio-group.modern {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.radio-card {
    background-color: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    flex: 1 1 calc(50% - 15px);
    min-width: 180px;
    position: relative;
    overflow: hidden;
}

.radio-card.large {
    flex: 1 1 100%;
}

.radio-card:hover {
    border-color: #90cdf4;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.radio-card.selected {
    border-color: #480EB7;
    background-color: #f0ecff;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(72, 14, 183, 0.2);
}

.radio-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(72, 14, 183, 0.1) 0%, rgba(108, 60, 230, 0.1) 100%);
    pointer-events: none;
}

.radio-card input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.radio-card label {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    font-weight: 500;
    margin: 0;
}

.radio-card .radio-icon {
    margin-right: 8px;
    font-size: 1.2rem;
    color: #cbd5e0;
    transition: all 0.3s;
}

.radio-card.selected .radio-icon {
    color: #480EB7;
    transform: scale(1.2);
}

.radio-card label span {
    flex: 1;
}

/* Botões de navegação */
.nav-buttons {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
}

.next-btn, .prev-btn, .submit-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.next-btn, .submit-btn {
    background: linear-gradient(135deg, #480EB7 0%, #6C3CE6 100%);
    color: white;
    min-width: 140px;
    box-shadow: 0 3px 10px rgba(72, 14, 183, 0.3);
}

.prev-btn {
    background-color: #e2e8f0;
    color: #4a5568;
    min-width: 130px;
}

.next-btn:hover, .submit-btn:hover {
    background: linear-gradient(135deg, #6C3CE6 0%, #8257FA 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(72, 14, 183, 0.5);
}

.prev-btn:hover {
    background-color: #cbd5e0;
    transform: translateY(-2px);
}

.next-btn i, .prev-btn i, .submit-btn i {
    margin: 0 8px;
}

.submit-btn {
    background: linear-gradient(135deg, #480EB7 0%, #8257FA 100%);
    min-width: 180px;
    box-shadow: 0 3px 10px rgba(72, 14, 183, 0.3);
    font-size: 1.05rem;
}

/* Rodapé */
footer {
    padding: 20px 30px;
    background: linear-gradient(135deg, #480EB7 0%, #6C3CE6 100%);
    color: white;
    text-align: center;
    font-size: 0.875rem;
    border-radius: 0 0 15px 15px;
}

footer p {
    margin-bottom: 5px;
    opacity: 0.9;
}

/* Responsividade */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    header h2 {
        font-size: 1.2rem;
    }
    
    .zucro-form {
        padding: 20px 15px;
    }
    
    .radio-group.modern {
        flex-direction: column;
    }
    
    .radio-card {
        flex: 1 1 100%;
    }
    
    .field-tooltip {
        display: none;
    }
    
    .nav-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .prev-btn, .next-btn, .submit-btn {
        width: 100%;
    }
}

/* Otimização adicional para dispositivos móveis */
@media (max-width: 480px) {
    .container {
        padding: 0;
    }
    
    .form-container {
        border-radius: 12px;
        margin-bottom: 15px;
        box-shadow: 0 10px 25px rgba(50, 10, 100, 0.2);
    }
    
    .banner-container {
        max-height: 180px;
    }
    
    .benefits {
        padding: 20px 15px;
    }
    
    .benefit-card {
        min-height: 100px;
        margin-bottom: 5px;
    }
    
    .benefit-icon {
        width: 50px;
        font-size: 20px;
    }
    
    .benefit-content h4 {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }
    
    .benefit-content p {
        font-size: 0.8rem;
    }
    
    .highlight {
        padding: 15px;
        margin-top: 15px;
    }
    
    .highlight p {
        font-size: 0.9rem;
    }
    
    /* Barra de progresso para mobile */
    .progress-container {
        padding: 10px 15px;
    }
    
    .progress-bar {
        height: 6px;
    }
    
    .progress-text {
        font-size: 0.75rem;
    }
    
    /* Elementos do formulário para mobile */
    .form-group {
        margin-bottom: 20px;
    }
    
    label {
        font-size: 0.95rem;
    }
    
    .field-icon {
        font-size: 0.9rem;
    }
    
    small, .field-note, .field-important {
        font-size: 0.8rem;
    }
    
    .question-highlight, .question-note {
        font-size: 0.85rem;
        padding: 6px 10px;
    }
    
    input[type="text"],
    input[type="email"],
    input[type="number"],
    textarea {
        padding: 10px 12px;
        font-size: 0.95rem;
    }
    
    /* Radio cards para mobile */
    .radio-card {
        padding: 2px;
        margin-bottom: 8px;
        min-height: auto;
    }
    
    .radio-card label {
        padding: 10px;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
    }
    
    .radio-card .radio-icon {
        font-size: 1rem;
        min-width: 20px;
    }
    
    .radio-card label span {
        line-height: 1.2;
    }
    
    /* Cards selecionados no mobile */
    .radio-card.selected {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(72, 14, 183, 0.15);
    }
    
    /* Botões de navegação para mobile */
    .nav-buttons {
        margin-top: 20px;
        gap: 8px;
    }
    
    .next-btn, .prev-btn, .submit-btn {
        padding: 10px 15px;
        font-size: 0.95rem;
        height: 42px;
    }
    
    .next-btn i, .prev-btn i, .submit-btn i {
        margin: 0 5px;
        font-size: 0.9rem;
    }
    
    /* Popup styling para mobile */
    .popup-container {
        width: 95%;
    }
    
    .popup-header {
        padding: 15px;
    }
    
    .popup-header h3 {
        font-size: 1.3rem;
    }
    
    .popup-content {
        padding: 15px;
    }
    
    .popup-main-message {
        font-size: 1.1rem;
    }
    
    .popup-emphasis {
        font-size: 1.2rem;
    }
}

/* Otimização para dispositivos muito pequenos */
@media (max-width: 320px) {
    .benefit-content h4 {
        font-size: 0.8rem;
    }
    
    .benefit-content p {
        font-size: 0.75rem;
    }
    
    .benefit-icon {
        width: 40px;
        font-size: 16px;
    }
    
    label {
        font-size: 0.9rem;
    }
    
    input[type="text"],
    input[type="email"],
    input[type="number"],
    textarea {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
    
    .radio-card label {
        padding: 8px;
        font-size: 0.85rem;
    }
}

/* Animações e efeitos */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.4); }
    70% { box-shadow: 0 0 0 6px rgba(66, 153, 225, 0); }
    100% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.form-group.focused {
    transform: translateY(-3px);
    transition: transform 0.3s ease;
}

.input-wrapper.valid input, 
.input-wrapper.valid textarea {
    border-color: #48bb78;
    background-color: #f0fff4;
}

.spotlight {
    animation: spotlight 2s infinite alternate;
}

@keyframes spotlight {
    0% { box-shadow: 0 0 10px rgba(72, 14, 183, 0.2); }
    100% { box-shadow: 0 0 20px rgba(72, 14, 183, 0.5); }
}

/* Estilo para formulário incompleto */
.input-incomplete {
    border-color: #fbd38d !important;
    background-color: #fffaf0 !important;
}

/* Toasts para mensagens */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    transform: translateX(150%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background-color: #6C3CE6;
}

.toast.warning {
    background-color: #ed8936;
}

.toast.error {
    background-color: #f56565;
}

.toast i {
    margin-right: 8px;
}

/* Validação de erros */
.input-error {
    border-color: #e53e3e !important;
    background-color: #fff5f5 !important;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1) !important;
}

/* Popup Styling */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.75);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.popup-overlay.active {
    opacity: 1;
    visibility: visible;
}

.popup-container {
    background: white;
    width: 90%;
    max-width: 500px;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    animation: popupEnter 0.5s forwards;
    transform: scale(0.9) translateY(-20px);
}

@keyframes popupEnter {
    to {
        transform: scale(1) translateY(0);
    }
}

.popup-header {
    background: linear-gradient(135deg, #480EB7 0%, #6C3CE6 100%);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    position: relative;
}

.popup-header i {
    font-size: 24px;
    margin-right: 15px;
}

.popup-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.popup-close {
    position: absolute;
    right: 15px;
    top: 15px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.popup-close:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: rotate(90deg);
}

.popup-content {
    padding: 25px 20px;
    text-align: center;
}

.popup-main-message {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2d3748;
}

.popup-main-message span {
    color: #480EB7;
    font-weight: 800;
    font-size: 1.4rem;
}

.popup-emphasis {
    font-size: 1.5rem;
    font-weight: 700;
    color: #480EB7;
    margin-bottom: 20px;
}

.popup-divider {
    height: 2px;
    background: linear-gradient(90deg, rgba(72,14,183,0.1), rgba(72,14,183,0.7), rgba(72,14,183,0.1));
    margin: 15px 0;
}

.popup-note {
    background-color: #f8fafc;
    padding: 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    color: #4a5568;
    text-align: left;
    border-left: 3px solid #480EB7;
    margin-top: 15px;
}

.popup-note i {
    color: #480EB7;
    margin-right: 8px;
}

@media (max-width: 600px) {
    .popup-main-message {
        font-size: 1.1rem;
    }
    
    .popup-emphasis {
        font-size: 1.2rem;
    }
} 