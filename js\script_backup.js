document.addEventListener('DOMContentLoaded', function() {
    // Elementos do formulário
    const form = document.querySelector('.zucro-form');
    const phoneInput = document.getElementById('phone');
    const instagramInput = document.getElementById('instagram');
    const submitBtn = document.querySelector('.submit-btn');
    const sections = document.querySelectorAll('.form-section');
    const nextButtons = document.querySelectorAll('.next-btn');
    const prevButtons = document.querySelectorAll('.prev-btn');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const reasonTextarea = document.getElementById('reason');
    const reasonCounter = document.getElementById('reasonCounter');
    
    // Elementos do popup
    const popup = document.getElementById('initialPopup');
    const closePopupBtn = document.getElementById('closePopup');
    
    console.log("Inicializando formulário...");
    
    // Configuração dos radio buttons para selecionar corretamente
    const radioInputs = document.querySelectorAll('input[type="radio"]');
    radioInputs.forEach(radio => {
        // Verifica estado inicial
        if (radio.checked) {
            const card = radio.closest('.radio-card');
            if (card) {
                card.classList.add('selected');
            }
        }
        
        // Adiciona evento de click
        radio.addEventListener('change', function() {
            // Encontra o grupo de radio buttons (todos com o mesmo name)
            const name = this.name;
            const group = document.querySelectorAll(`input[name="${name}"]`);
            
            // Remove a classe 'selected' de todos os cards do grupo
            group.forEach(groupRadio => {
                const groupCard = groupRadio.closest('.radio-card');
                if (groupCard) {
                    groupCard.classList.remove('selected');
                }
            });
            
            // Adiciona classe 'selected' ao card selecionado
            const selectedCard = this.closest('.radio-card');
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }
        });
        
        // Adiciona evento de click no próprio card para melhorar UX
        const parentCard = radio.closest('.radio-card');
        if (parentCard) {
            parentCard.addEventListener('click', function() {
                const radioInput = this.querySelector('input[type="radio"]');
                if (radioInput) {
                    radioInput.checked = true;
                    // Dispara o evento change manualmente
                    const event = new Event('change');
                    radioInput.dispatchEvent(event);
                }
            });
        }
    });
    
    // Adiciona eventos para destacar inputs ao focar
    const allInputs = document.querySelectorAll('input, textarea');
    allInputs.forEach(input => {
        // Quando o input recebe foco
        input.addEventListener('focus', function() {
            // Encontrar o form-group pai
            const formGroup = this.closest('.form-group');
            if (formGroup) {
                formGroup.classList.add('focused');
                
                // Adiciona classe à div input-wrapper para styling adicional
                const inputWrapper = this.closest('.input-wrapper');
                if (inputWrapper) {
                    inputWrapper.classList.add('input-focused');
                }
            }
        });
        
        // Quando o input perde foco
        input.addEventListener('blur', function() {
            // Encontrar o form-group pai
            const formGroup = this.closest('.form-group');
            if (formGroup) {
                formGroup.classList.remove('focused');
                
                // Remove classe da div input-wrapper
                const inputWrapper = this.closest('.input-wrapper');
                if (inputWrapper) {
                    inputWrapper.classList.remove('input-focused');
                    
                    // Se o campo tem valor, marca como preenchido
                    if (this.value.trim() !== '') {
                        inputWrapper.classList.add('valid');
                    } else {
                        inputWrapper.classList.remove('valid');
                    }
                }
            }
        });
    });
    
    // Exibir o popup com um pequeno atraso para garantir uma experiência melhor
    setTimeout(function() {
        if (popup) {
            popup.classList.add('active');
        }
    }, 800);
    
    // Fechar o popup quando o botão de fechar for clicado
    if (closePopupBtn) {
        closePopupBtn.addEventListener('click', function() {
            popup.classList.remove('active');
            
            // Remover completamente após a animação terminar
            setTimeout(function() {
                popup.style.display = 'none';
            }, 300);
        });
    }
    
    // Inicializa o contador de caracteres
    if (reasonTextarea && reasonCounter) {
        reasonTextarea.addEventListener('input', function() {
            const currentLength = this.value.length;
            reasonCounter.textContent = `${currentLength}/500 caracteres`;
            
            // Atualiza cor com base no comprimento
            if (currentLength < 100) {
                reasonCounter.style.color = '#e53e3e';
            } else if (currentLength < 200) {
                reasonCounter.style.color = '#ed8936';
            } else {
                reasonCounter.style.color = '#48bb78';
            }
        });
        
        // Inicializa contador
        if (reasonTextarea.value) {
            const initialLength = reasonTextarea.value.length;
            reasonCounter.textContent = `${initialLength}/500 caracteres`;
        }
    }
    
    // Auto-foco no primeiro campo ao carregar
    const firstInput = document.querySelector('.form-section.active input');
    if (firstInput) {
        setTimeout(() => {
            firstInput.focus();
        }, 500);
    }

    // Função simplificada de navegação entre seções
    function showSection(sectionId) {
        console.log("Mostrando seção:", sectionId);
        
        // Esconde todas as seções e remove required dos campos ocultos
        sections.forEach(section => {
            section.style.display = 'none';
            // Remove required de todos os campos da seção oculta
            const inputs = section.querySelectorAll('input[required], textarea[required]');
            inputs.forEach(input => {
                input.removeAttribute('required');
                input.setAttribute('data-required', 'true'); // Marca que era required
            });
        });
        
        // Mostra a seção solicitada
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.style.display = 'block';
            
            // Adiciona required de volta aos campos da seção visível
            const visibleInputs = targetSection.querySelectorAll('input[data-required], textarea[data-required]');
            visibleInputs.forEach(input => {
                input.setAttribute('required', 'required');
            });
            
            // Atualiza o progresso
            let sectionIndex = 0;
            sections.forEach((section, index) => {
                if (section.id === sectionId) {
                    sectionIndex = index;
                }
            });
            
            const progress = (sectionIndex / (sections.length - 1)) * 100;
            if (progressFill) progressFill.style.width = progress + '%';
            if (progressText) progressText.textContent = `Progresso: ${Math.round(progress)}%`;
        }
    }
    
    // Manipuladores de eventos para botões de navegação
    if (nextButtons && nextButtons.length > 0) {
        nextButtons.forEach(button => {
            console.log("Configurando botão próximo");
            button.addEventListener('click', function() {
                const nextSection = this.getAttribute('data-next');
                console.log("Botão próximo clicado, indo para:", nextSection);
                showSection(nextSection);
            });
        });
    }
    
    if (prevButtons && prevButtons.length > 0) {
        prevButtons.forEach(button => {
            console.log("Configurando botão anterior");
            button.addEventListener('click', function() {
                const prevSection = this.getAttribute('data-prev');
                console.log("Botão anterior clicado, indo para:", prevSection);
                showSection(prevSection);
            });
        });
    }

    // Configuração do campo de telefone para formato DDNUMERO
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // Remove qualquer caractere que não seja número
            let value = this.value.replace(/\D/g, '');
            
            // Limita a 11 dígitos (DDD + Número)
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            
            // Atualiza o valor do campo
            this.value = value;
        });
    }

    // Formatação para o Instagram
    if (instagramInput) {
        instagramInput.addEventListener('input', function(e) {
            // Se o usuário não digitou @, adiciona automaticamente
            if (this.value && !this.value.startsWith('@')) {
                this.value = '@' + this.value;
            }
        });
    }
    
    // Validação de formulário    if (form) {        form.addEventListener('submit', function(e) {            let isValid = true;                        // Primeiro, restaura required em todos os campos que deveriam tê-lo            const allRequiredInputs = form.querySelectorAll('input[data-required="true"], textarea[data-required="true"]');            allRequiredInputs.forEach(input => {                input.setAttribute('required', 'required');            });                        // Validação básica de campos obrigatórios            const requiredInputs = form.querySelectorAll('input[required], textarea[required]');                        requiredInputs.forEach(input => {                if (!input.value.trim()) {                    isValid = false;                    input.classList.add('input-error');                } else {                    input.classList.remove('input-error');                }            });
            
            // Validação específica para telefone
            if (phoneInput && phoneInput.value) {
                // Verifica se tem entre 10 e 11 dígitos
                const phoneValue = phoneInput.value.replace(/\D/g, '');
                if (phoneValue.length < 10 || phoneValue.length > 11) {
                    isValid = false;
                    phoneInput.classList.add('input-error');
                    showToast('Telefone deve ter entre 10 e 11 dígitos (DDD + número)', 'error');
                }
            }
            
            if (!isValid) {
                e.preventDefault();
                showToast('Por favor, corrija os erros antes de enviar', 'error');
                return false;
            }
            
            // Se tudo estiver ok, continua com o envio
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            submitBtn.disabled = true;
        });
    }
    
    // Sistema de Toast para notificações
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        let icon = '';
        switch(type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-times-circle"></i>';
                break;
        }
        
        toast.innerHTML = `${icon} ${message}`;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
    
            // Inicializa a exibição da primeira seção    console.log("Seções disponíveis:", sections.length);    if (sections.length > 0) {        // Define a primeira seção como visível        sections.forEach((section, index) => {            if (index === 0) {                section.style.display = 'block';                // Mantém required nos campos da primeira seção                const inputs = section.querySelectorAll('input, textarea');                inputs.forEach(input => {                    if (input.hasAttribute('required')) {                        input.setAttribute('data-required', 'true');                    }                });            } else {                section.style.display = 'none';                // Remove required dos campos das seções ocultas                const inputs = section.querySelectorAll('input[required], textarea[required]');                inputs.forEach(input => {                    input.removeAttribute('required');                    input.setAttribute('data-required', 'true');                });            }        });    }}); 