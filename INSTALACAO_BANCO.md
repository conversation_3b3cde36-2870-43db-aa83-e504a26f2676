# 📋 Instruções de Instalação do Banco de Dados

## 🚀 Passos para configurar o banco de dados:

### 1. Acesse o phpMyAdmin
- Abra o navegador e acesse: `http://localhost/phpmyadmin`
- Faça login (usuário padrão: `root`, senha: vazio)

### 2. Execute o Script SQL
- Clique na aba "SQL" no phpMyAdmin
- Copie todo o conteúdo do arquivo `database/criar_banco.sql`
- Cole no campo de texto
- Clique em "Executar"

### 3. Verifique a criação
- O banco de dados `formulario_zucro` deve aparecer na lista à esquerda
- Ao clicar nele, você verá duas tabelas:
  - `inscritos` - Para armazenar os formulários
  - `administradores` - Para os usuários admin

### 4. Configure a conexão (se necessário)
- Se seu MySQL tem senha, edite o arquivo `database/config.php`:
```php
define('DB_USER', 'root');     // Seu usuário
define('DB_PASS', 'suasenha'); // Sua senha
```

## ✅ Pronto!
Agora o sistema está configurado para salvar os formulários no banco de dados.

## 🔐 Credenciais do Admin
- **Usuário**: admin
- **Senha**: zucro2025

## 📊 Estrutura das Tabelas

### Tabela `inscritos`:
- `id` - ID único
- `nome` - Nome completo
- `email` - Email
- `telefone` - Telefone
- `instagram` - Instagram
- `idade` - Idade
- `experiencia` - Experiência em marketing
- `disponibilidade` - Disponibilidade para mentoria
- `investimento` - Disposição para investir
- `motivo` - Por que deve ser aceito
- `data_inscricao` - Data/hora do cadastro
- `ip_address` - IP do usuário
- `status` - Status do formulário (novo, analisado, aprovado, rejeitado)
- `observacoes` - Observações do admin

### Tabela `administradores`:
- `id` - ID único
- `usuario` - Nome de usuário
- `senha` - Senha (hash)
- `nome_completo` - Nome completo
- `email` - Email
- `ativo` - Se está ativo
- `data_criacao` - Data de criação
- `ultimo_acesso` - Último acesso

## 🛠️ Solução de Problemas

### Erro de conexão?
1. Verifique se o XAMPP está rodando (Apache e MySQL)
2. Confirme usuário e senha no `database/config.php`
3. Teste a conexão acessando o phpMyAdmin

### Erro ao salvar formulário?
1. Verifique se o banco foi criado corretamente
2. Confirme se as tabelas existem
3. Verifique as permissões do MySQL 