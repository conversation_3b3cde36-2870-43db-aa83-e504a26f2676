<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subs<PERSON>cio <PERSON>ro - Formulário de Inscrição</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <!-- Popup de Atenão -->
    <div class="popup-overlay" id="initialPopup">
        <div class="popup-container">
            <div class="popup-header">
                <i class="fas fa-exclamation-circle"></i>
                <h3>ATENÃO</h3>
                <button class="popup-close" id="closePopup">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="popup-content">
                <p class="popup-main-message">SÃO APENAS <span>10 VAGAS</span>, E TODOS SERÃO ANALISADOS!</p>
                <p class="popup-emphasis">PREENCHAM COM CUIDADO</p>
                <div class="popup-divider"></div>
                <p class="popup-note"><i class="fas fa-info-circle"></i> Sua aplicação não garante a vaga. Faremos uma análise estratégica e caso você seja aprovado(a), entraremos em contato pelo whatsapp.</p>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="form-container">
            <div class="banner-container">
                <img src="formulario.png" alt="Banner Zucro" class="banner-image">
            </div>
            
            <section class="benefits">
                <h3>No treinamento você vai receber:</h3>
                <div class="benefits-grid">
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="benefit-content">
                            <h4>🚀 Benefício de Ser Aluno do Milionário com I.A</h4>
                            <p>Aprenda a usar a inteligência artificial para criar renda recorrente e escalável no digital.</p>
                            <div class="benefit-tag">Exclusivo</div>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="benefit-content">
                            <h4>🤖 Máquina de Automação</h4>
                            <p>Ferramentas de I.A poderosas para atrair clientes e gerar vendas de forma automática.</p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="benefit-content">
                            <h4>🔥 Funil de Vendas com I.A</h4>
                            <p>Estratégias comprovadas para transformar leads em contratos e faturamento real.</p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <div class="benefit-content">
                            <h4>📄 Contratos e Modelos Profissionais</h4>
                            <p>Documentos prontos para fechar negócios com segurança e credibilidade.</p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="benefit-content">
                            <h4>💬 Scripts de Abordagem com I.A</h4>
                            <p>Textos persuasivos criados pela inteligência artificial para encantar e converter clientes desde o primeiro contato.</p>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="benefit-content">
                            <h4>⭐ Certificação Oficial Milionário com I.A</h4>
                            <p>Valide seu aprendizado com um certificado exclusivo e aumente sua autoridade no mercado.</p>
                            <div class="benefit-tag">Premium</div>
                        </div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="benefit-content">
                            <h4>🤝 Grupo de Network</h4>
                            <p>Comunidade fechada com empreendedores aplicando I.A para gerar negócios milionários.</p>
                        </div>
                    </div>
                </div>
                
                <div class="highlight">
                    <p><strong>🛬 PRÓXIMO DESAFIO DA SDZL: DESAFIO DE DUBAI, ONDE LEVAREMOS 1 PLAYER COM TUDO PAGO PARA VIAJAR COM O VICTOR.</strong></p>
                    <p><strong>EVENTOS E DESAFIOS QUE FAZEMOS! IREMOS INICIAR ESSE MÊS O DESAFIO DE DUBAI, QUEM VENDER MAIS SITE VAI GANHAR UMA PASSAGEM PARA VIAJAR FINAL DO ANO.</strong></p>
                    <p><em>*Sua aplicação não garante a vaga. Faremos uma análise estratégica e caso você seja aprovado(a), entraremos em contato pelo whatsapp.</em></p>
                    <p><em>ASS: Victor Gronn</em></p>
                </div>
            </section>
            
            <?php
            // Inclui o arquivo de configuração do banco de dados
            require_once 'database/config.php';
            
            // Inicializa variáveis para manter os valores do formulário
            $phone = $instagram = $age = $experience = $name = $email = $availability = $investment = $reason = '';
            $errors = [];
            $success = false;
            
            // Processa o formulário quando enviado
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Validação dos campos
                if (empty($_POST['phone'])) {
                    $errors[] = 'Número de telefone é obrigatório';
                } else {
                    $phone = filter_var($_POST['phone'], FILTER_SANITIZE_STRING);
                }
                
                if (empty($_POST['instagram'])) {
                    $errors[] = 'Instagram é obrigatório';
                } else {
                    $instagram = filter_var($_POST['instagram'], FILTER_SANITIZE_STRING);
                }
                
                if (empty($_POST['age'])) {
                    $errors[] = 'Idade é obrigatória';
                } else {
                    $age = filter_var($_POST['age'], FILTER_SANITIZE_STRING);
                }
                
                if (empty($_POST['experience'])) {
                    $errors[] = 'Experiência é obrigatória';
                } else {
                    $experience = $_POST['experience'];
                }
                
                if (empty($_POST['name'])) {
                    $errors[] = 'Nome é obrigatório';
                } else {
                    $name = filter_var($_POST['name'], FILTER_SANITIZE_STRING);
                }
                
                if (empty($_POST['email'])) {
                    $errors[] = 'Email é obrigatório';
                } else {
                    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = 'Email inválido';
                    }
                }
                
                if (empty($_POST['availability'])) {
                    $errors[] = 'Disponibilidade é obrigatória';
                } else {
                    $availability = $_POST['availability'];
                }
                
                if (empty($_POST['investment'])) {
                    $errors[] = 'Resposta sobre investimento é obrigatória';
                } else {
                    $investment = $_POST['investment'];
                }
                
                if (empty($_POST['reason'])) {
                    $errors[] = 'Motivo é obrigatório';
                } else {
                    $reason = filter_var($_POST['reason'], FILTER_SANITIZE_STRING);
                }
                
                // Se não há erros, processa o formulário
                if (empty($errors)) {
                    // Debug: mostra que chegou aqui
                    error_log("Processando formulário - sem erros de validação");
                    
                    // Conecta ao banco de dados
                    $conn = conectarBanco();
                    
                    if ($conn) {
                        error_log("Conexão com banco estabelecida");
                        // Prepara os dados para inserção
                        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
                        
                        // Query SQL para inserir os dados
                        $sql = "INSERT INTO inscritos (nome, email, telefone, instagram, idade, experiencia, disponibilidade, investimento, motivo, ip_address) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        
                        $stmt = $conn->prepare($sql);
                        
                        if ($stmt) {
                            $stmt->bind_param("ssssssssss", 
                                $name, 
                                $email, 
                                $phone, 
                                $instagram, 
                                $age, 
                                $experience, 
                                $availability, 
                                $investment, 
                                $reason,
                                $ip_address
                            );
                            
                            if ($stmt->execute()) {
                                $success = true;
                                
                                // Também salva no arquivo texto como backup
                                $data = "Nome: $name\n";
                                $data .= "Email: $email\n";
                                $data .= "Telefone: $phone\n";
                                $data .= "Instagram: $instagram\n";
                                $data .= "Idade: $age\n";
                                $data .= "Experiência: $experience\n";
                                $data .= "Disponibilidade: $availability\n";
                                $data .= "Investimento: $investment\n";
                                $data .= "Motivo: $reason\n";
                                $data .= "Data: " . date('Y-m-d H:i:s') . "\n\n";
                                
                                $file = fopen("inscritos.txt", "a");
                                if ($file) {
                                    fwrite($file, $data);
                                    fclose($file);
                                }
                                
                                // Limpa os campos após envio bem-sucedido
                                $phone = $instagram = $age = $experience = $name = $email = $availability = $investment = $reason = '';
                            } else {
                                $errors[] = 'Erro ao salvar no banco de dados. Por favor, tente novamente.';
                            }
                            
                            $stmt->close();
                        } else {
                            $errors[] = 'Erro ao preparar a consulta. Por favor, tente novamente.';
                        }
                        
                        $conn->close();
                    } else {
                        $errors[] = 'Erro ao conectar com o banco de dados. Por favor, tente novamente.';
                    }
                }
            }
            ?>
            
            <?php if ($success): ?>
                <div class="success-message">
                    <h3><i class="fas fa-check-circle"></i> Inscrião enviada com sucesso!</h3>
                    <p>Agradecemos seu interesse. Caso você seja aprovado(a), entraremos em contato pelo WhatsApp.</p>
                    <div class="success-animation">
                        <lottie-player src="https://lottie.host/bed1f1e4-a5f2-4fcb-9a5b-e9f6c3144940/GH9q6tCGmU.json" background="transparent" speed="1" style="width: 150px; height: 150px; margin: 0 auto;" loop autoplay></lottie-player>
                    </div>
                </div>
            <?php else: ?>
                <?php if (!empty($errors)): ?>
                    <div class="error-container">
                        <h3><i class="fas fa-exclamation-triangle"></i> Por favor, corrija os seguintes erros:</h3>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Progresso: 0%</div>
                </div>
                
                <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="zucro-form" novalidate>
                    <div class="form-section" id="section1" style="display:block;">
                        <div class="form-group required">
                            <label for="name">
                                <i class="fas fa-user field-icon"></i> NOME COMPLETO *
                            </label>
                            <div class="input-wrapper">
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" placeholder="Digite seu nome completo" required autocomplete="name">
                                <div class="field-tooltip">Utilize seu nome completo para facilitar nossa comunicação</div>
                                <div class="valid-indicator"><i class="fas fa-check"></i></div>
                            </div>
                        </div>
                        
                        <div class="form-group required">
                            <label for="email">
                                <i class="fas fa-envelope field-icon"></i> EMAIL *
                            </label>
                            <div class="input-wrapper">
                                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" placeholder="<EMAIL>" required autocomplete="email">
                                <div class="field-tooltip">Utilizaremos este email para enviar informações importantes</div>
                                <div class="valid-indicator"><i class="fas fa-check"></i></div>
                            </div>
                        </div>
                        
                        <div class="form-group required">
                            <label for="phone">
                                <i class="fas fa-phone-alt field-icon"></i> WHATSAPP (COM DDD) *
                            </label>
                            <div class="input-wrapper">
                                <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($phone); ?>" placeholder="DDNUMERO" required autocomplete="tel">
                                <small class="field-tip">Atenção ao colocar o número correto, entraremos em contato por ele!</small>
                                <div class="field-tooltip">Formato: DDNUMERO - Apenas números, sem espaços ou caracteres especiais</div>
                                <div class="valid-indicator"><i class="fas fa-check"></i></div>
                            </div>
                        </div>
                        
                        <div class="nav-buttons">
                            <button type="button" class="next-btn" data-next="section2"><i class="fas fa-arrow-right"></i> Próximo</button>
                        </div>
                    </div>
                    
                    <div class="form-section" id="section2">
                        <div class="form-group required">
                            <label for="instagram">
                                <i class="fab fa-instagram field-icon"></i> INSTAGRAM *
                            </label>
                            <div class="input-wrapper">
                                <input type="text" id="instagram" name="instagram" value="<?php echo htmlspecialchars($instagram); ?>" placeholder="@seu_instagram" required>
                                <div class="field-tooltip">Adicione seu @ para conectarmos nas redes sociais</div>
                                <div class="valid-indicator"><i class="fas fa-check"></i></div>
                            </div>
                        </div>
                        
                        <div class="form-group required">
                            <label for="age">
                                <i class="fas fa-birthday-cake field-icon"></i> SUA IDADE *
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="age" name="age" value="<?php echo htmlspecialchars($age); ?>" min="18" max="100" placeholder="Idade em anos" required>
                                <div class="field-tooltip">Precisamos saber sua idade para personalizar a experiência</div>
                                <div class="valid-indicator"><i class="fas fa-check"></i></div>
                            </div>
                        </div>
                        
                        <div class="form-group required">
                            <label>
                                <i class="fas fa-briefcase field-icon"></i> EXPERIÊNCIA COM MARKETING DIGITAL *
                            </label>
                            <small class="field-note"><em>(LEMBRANDO QUE NÃO PRECISA DE EXPERIÊNCIA PARA COMEÇAR)</em></small>
                            <div class="radio-group modern">
                                <div class="radio-card <?php echo ($experience === 'TENHO EXPERIÊNCIA') ? 'selected' : ''; ?>">
                                    <input type="radio" id="exp_yes" name="experience" value="TENHO EXPERIÊNCIA" <?php echo ($experience === 'TENHO EXPERIÊNCIA') ? 'checked' : ''; ?> required>
                                    <label for="exp_yes">
                                        <i class="fas fa-check-circle radio-icon"></i>
                                        <span>TENHO EXPERIÊNCIA</span>
                                    </label>
                                </div>
                                <div class="radio-card <?php echo ($experience === 'NÃO TENHO EXPERIÊNCIA') ? 'selected' : ''; ?>">
                                    <input type="radio" id="exp_no" name="experience" value="NÃO TENHO EXPERIÊNCIA" <?php echo ($experience === 'NÃO TENHO EXPERIÊNCIA') ? 'checked' : ''; ?>>
                                    <label for="exp_no">
                                        <i class="fas fa-check-circle radio-icon"></i>
                                        <span>NÃO TENHO EXPERIÊNCIA</span>
                                    </label>
                                </div>
                                <div class="radio-card <?php echo ($experience === 'SOU INTERMEDIÁRIO') ? 'selected' : ''; ?>">
                                    <input type="radio" id="exp_int" name="experience" value="SOU INTERMEDIRIO" <?php echo ($experience === 'SOU INTERMEDIÁRIO') ? 'checked' : ''; ?>>
                                    <label for="exp_int">
                                        <i class="fas fa-check-circle radio-icon"></i>
                                        <span>SOU INTERMEDIÁRIO</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="nav-buttons">
                            <button type="button" class="prev-btn" data-prev="section1"><i class="fas fa-arrow-left"></i> Anterior</button>
                            <button type="button" class="next-btn" data-next="section3"><i class="fas fa-arrow-right"></i> Próximo</button>
                        </div>
                    </div>
                    
                    <div class="form-section" id="section3">
                        <div class="form-group required">
                            <label>
                                <i class="fas fa-calendar-check field-icon"></i> DISPONIBILIDADE PARA MENTORIA MENSAL *
                            </label>
                            <div class="radio-group modern">
                                <div class="radio-card large <?php echo ($availability === 'Sim, me comprometo com a comunidade afins de ter resultados expressivos') ? 'selected' : ''; ?>">
                                    <input type="radio" id="avail_yes" name="availability" value="Sim, me comprometo com a comunidade afins de ter resultados expressivos" <?php echo ($availability === 'Sim, me comprometo com a comunidade afins de ter resultados expressivos') ? 'checked' : ''; ?> required>
                                    <label for="avail_yes">
                                        <i class="fas fa-check-circle radio-icon"></i>
                                        <span>Sim, me comprometo com a comunidade para ter resultados expressivos</span>
                                    </label>
                                </div>
                                <div class="radio-card large <?php echo ($availability === 'Não tenho muito tempo') ? 'selected' : ''; ?>">
                                    <input type="radio" id="avail_no" name="availability" value="Não tenho muito tempo" <?php echo ($availability === 'Não tenho muito tempo') ? 'checked' : ''; ?>>
                                    <label for="avail_no">
                                        <i class="fas fa-check-circle radio-icon"></i>
                                        <span>Não tenho muito tempo disponível</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group required">
                            <label>
                                <i class="fas fa-hand-holding-usd field-icon"></i> INVESTIMENTO DE 12x de 149,95 ou 1.497 A VISTA *
                            </label>
                            <p class="question-note">Você investiria esse valor e tempo em voc e seu negócio? (lembrando que um contrato já recupera o valor investido)</p>
                            <div class="radio-group modern">
                                <div class="radio-card large <?php echo ($investment === 'Claro, com certeza') ? 'selected' : ''; ?>">
                                    <input type="radio" id="invest_yes" name="investment" value="Claro, com certeza" <?php echo ($investment === 'Claro, com certeza') ? 'checked' : ''; ?> required>
                                    <label for="invest_yes">
                                        <i class="fas fa-check-circle radio-icon"></i>
                                        <span>Claro, com certeza investiria</span>
                                    </label>
                                </div>
                                <div class="radio-card large <?php echo ($investment === 'Não, pois estou sem dinheiro no momento') ? 'selected' : ''; ?>">
                                    <input type="radio" id="invest_no" name="investment" value="Não, pois estou sem dinheiro no momento" <?php echo ($investment === 'Não, pois estou sem dinheiro no momento') ? 'checked' : ''; ?>>
                                    <label for="invest_no">
                                        <i class="fas fa-check-circle radio-icon"></i>
                                        <span>Não, pois estou sem dinheiro no momento</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="nav-buttons">
                            <button type="button" class="prev-btn" data-prev="section2"><i class="fas fa-arrow-left"></i> Anterior</button>
                            <button type="button" class="next-btn" data-next="section4"><i class="fas fa-arrow-right"></i> Próximo</button>
                        </div>
                    </div>
                    
                    <div class="form-section" id="section4">
                        <div class="form-group required spotlight">
                            <label for="reason">
                                <i class="fas fa-star field-icon"></i> POR QUE DEVEMOS TE ACEITAR? *
                            </label>
                            <p class="question-highlight">São apenas 20 vagas por R$1.497, por qual motivo você acha que deveríamos te aceitar?</p>
                            <small class="field-important"><em>(Responda com atenção, isso pode te dar a chance de ser aceito(a) no treinamento)</em></small>
                            <div class="input-wrapper textarea-wrapper">
                                <textarea id="reason" name="reason" rows="6" placeholder="Descreva aqui por que você merece ser selecionado(a) para este treinamento exclusivo" required><?php echo htmlspecialchars($reason); ?></textarea>
                                <div class="char-counter" id="reasonCounter">0/500 caracteres</div>
                                <div class="field-tooltip">Esta é a pergunta mais importante do formulário. Seja sincero(a) e mostre seu potencial!</div>
                            </div>
                        </div>
                        
                        <div class="nav-buttons">
                            <button type="button" class="prev-btn" data-prev="section3"><i class="fas fa-arrow-left"></i> Anterior</button>
                            <button type="submit" class="submit-btn"><i class="fas fa-paper-plane"></i> Enviar Inscrição</button>
                        </div>
                    </div>
                </form>
            <?php endif; ?>
            
            <footer>
                <p>Este formulário foi criado para a Zucro. Todos os direitos reservados.</p>
                <p>Nunca envie senhas através deste formulário.</p>
            </footer>
        </div>
    </div>
    
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <script src="js/script.js"></script>
</body>
</html> 