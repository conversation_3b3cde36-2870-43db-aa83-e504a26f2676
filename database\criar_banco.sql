-- Criação do banco de dados
CREATE DATABASE IF NOT EXISTS formulario_zucro CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE formulario_zucro;

-- Criação da tabela de inscritos
CREATE TABLE IF NOT EXISTS inscritos (
    id INT(11) NOT NULL AUTO_INCREMENT,
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telefone VARCHAR(20) NOT NULL,
    instagram VARCHAR(100) NOT NULL,
    idade INT(3) NOT NULL,
    experiencia VARCHAR(50) NOT NULL,
    disponibilidade TEXT NOT NULL,
    investimento TEXT NOT NULL,
    motivo TEXT NOT NULL,
    data_inscricao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    status ENUM('novo', 'analisado', 'aprovado', 'rejeitado') DEFAULT 'novo',
    observacoes TEXT,
    PRIMARY KEY (id),
    INDEX idx_email (email),
    INDEX idx_data (data_inscricao),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Criação da tabela de administradores
CREATE TABLE IF NOT EXISTS administradores (
    id INT(11) NOT NULL AUTO_INCREMENT,
    usuario VARCHAR(50) NOT NULL UNIQUE,
    senha VARCHAR(255) NOT NULL,
    nome_completo VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ultimo_acesso TIMESTAMP NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserir administrador padrão (senha: zucro2025)
INSERT INTO administradores (usuario, senha, nome_completo, email) VALUES 
('admin', '$2y$10$YmI2ZTQ0NzQ0MmI4MjQ2NOl8jpEHtZGxXKxN6kxn.bTxKJnx6XO6', 'Administrador', '<EMAIL>'); 