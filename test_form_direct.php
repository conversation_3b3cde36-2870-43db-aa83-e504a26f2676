<?php
// Teste direto de formulário sem interface
require_once 'database/config.php';

echo "<h1>Teste Direto de Formulário</h1>";

// Conecta ao banco
$conn = conectarBanco();

if (!$conn) {
    die("<p style='color: red;'>Erro ao conectar ao banco de dados!</p>");
}

echo "<p style='color: green;'>✓ Conectado ao banco de dados!</p>";

// Dados de teste
$nome = "Teste Direto " . date('Y-m-d H:i:s');
$email = "<EMAIL>";
$telefone = "11987654321";
$instagram = "@teste_direto";
$idade = "30";
$experiencia = "NÃO TENHO EXPERIÊNCIA";
$disponibilidade = "Sim, me comprometo com a comunidade afins de ter resultados expressivos";
$investimento = "Claro, com certeza";
$motivo = "Este é um teste direto para verificar se o formulário está salvando no banco de dados corretamente.";
$ip_address = "127.0.0.1";

// Prepara e executa a query
$sql = "INSERT INTO inscritos (nome, email, telefone, instagram, idade, experiencia, disponibilidade, investimento, motivo, ip_address) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt = $conn->prepare($sql);

if (!$stmt) {
    die("<p style='color: red;'>Erro ao preparar query: " . $conn->error . "</p>");
}

$stmt->bind_param("ssssssssss", 
    $nome, 
    $email, 
    $telefone, 
    $instagram, 
    $idade, 
    $experiencia, 
    $disponibilidade, 
    $investimento, 
    $motivo,
    $ip_address
);

if ($stmt->execute()) {
    echo "<p style='color: green;'>✓ Dados inseridos com sucesso! ID: " . $stmt->insert_id . "</p>";
    
    // Salva no arquivo texto também
    $data = "Nome: $nome\n";
    $data .= "Email: $email\n";
    $data .= "Telefone: $telefone\n";
    $data .= "Instagram: $instagram\n";
    $data .= "Idade: $idade\n";
    $data .= "Experiência: $experiencia\n";
    $data .= "Disponibilidade: $disponibilidade\n";
    $data .= "Investimento: $investimento\n";
    $data .= "Motivo: $motivo\n";
    $data .= "Data: " . date('Y-m-d H:i:s') . "\n\n";
    
    $file = fopen("inscritos.txt", "a");
    if ($file) {
        fwrite($file, $data);
        fclose($file);
        echo "<p style='color: green;'>✓ Dados salvos no arquivo texto!</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Erro ao inserir: " . $stmt->error . "</p>";
}

$stmt->close();

// Mostra últimos registros
echo "<h2>Últimos 5 registros no banco:</h2>";
$result = $conn->query("SELECT id, nome, email, data_inscricao FROM inscritos ORDER BY id DESC LIMIT 5");

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Data</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 5px;'>" . $row['id'] . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($row['nome']) . "</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars($row['email']) . "</td>";
        echo "<td style='padding: 5px;'>" . $row['data_inscricao'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Nenhum registro encontrado.</p>";
}

$conn->close();

echo "<hr>";
echo "<p><a href='index.php'>Voltar ao formulário</a> | <a href='admin.php'>Ver painel admin</a></p>";
?> 